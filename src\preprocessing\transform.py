
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
# transform.py
class DataTransformer:
    def __init__(self, embedding_dim=16):
        self.embedding_dim = embedding_dim
        self.scalers = {}
        self.label_encoders = {}
        self.categorical_columns = []
        self.numeric_columns = []

    def fit_transform(self, df):
        df = df.copy()
        # Explicitly define categorical columns for Adult Income dataset
        # These are known to be categorical (object/string type or specific columns)
        categorical_override = [
            'workclass', 'education', 'marital_status', 'occupation', 'relationship',
            'race', 'sex', 'native_country', 'income'
        ]
        for col in df.columns:
            if df[col].dtype == 'object' or df[col].dtype.name == 'category' or col in categorical_override:
                self.categorical_columns.append(col)
                le = LabelEncoder()
                df[col] = le.fit_transform(df[col].astype(str))
                self.label_encoders[col] = le
                # Debug print for categorical columns
                # Removed categorical column debug print
            else:
                self.numeric_columns.append(col)
                scaler = StandardScaler()
                df[col] = scaler.fit_transform(df[[col]])
                self.scalers[col] = scaler
        df = df.dropna()
        return df
