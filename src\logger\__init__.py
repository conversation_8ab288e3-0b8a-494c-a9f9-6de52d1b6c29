# logger/__init__.py
import logging
from tqdm import tqdm

class Logger:
    def __init__(self):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s: %(message)s')
        self.logger = logging.getLogger('SDG')

    def info(self, msg):
        self.logger.info(msg)

    def warning(self, msg):
        self.logger.warning(msg)

    def error(self, msg):
        self.logger.error(msg)

    def get_progress_bar(self, iterable, desc=None):
        return tqdm(iterable, desc=desc)
