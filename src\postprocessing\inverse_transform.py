
import pandas as pd
import numpy as np
# inverse_transform.py
class InverseTransformer:
    def __init__(self, categorical_columns, label_encoders):
        self.categorical_columns = categorical_columns
        self.label_encoders = label_encoders

    def inverse_transform(self, df):
        for col in self.categorical_columns:
            le = self.label_encoders[col]
            df[col] = np.round(df[col]).astype(int)
            df[col] = le.inverse_transform(df[col].clip(0, len(le.classes_)-1))
        return df
