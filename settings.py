# settings.py
# All training and generation parameters in a dictionary

SETTINGS = {
    'epochs': 1000,
    'batch_size': 64,
    'patience': 10,
    'learning_rate_g': 5e-5,  # generator learning rate (lower)
    'learning_rate_d': 5e-5,  # discriminator learning rate (lower)
    'optimizer_g': 'Adam',  # optimizer for generator
    'optimizer_d': '<PERSON>',  # optimizer for discriminator
    'gradient_clip': 1.0,  # max norm for gradient clipping
    'dropout': 0.4,  # dropout rate for model layers (higher)
    'weight_decay': 0.0,  # L2 penalty
    'penalty_type': 'none',  # e.g. 'gradient', 'l1', 'l2', 'none'
    'penalty_lambda': 1.0,  # strength of penalty
    'latent_dim': 100,
    'embedding_dim': 16,
    'cuda': True,
    'log_interval': 1,
    'dataset': None,  # use CSV only, do not use covertype
    'output_dir': 'data/output',
    'generator_layers': [128, 256, 128],  # list of hidden layer sizes for generator
    'discriminator_layers': [128, 256, 128],  # list of hidden layer sizes for discriminator
    'd_steps': 5,  # number of discriminator steps per generator step
    'g_steps': 1,  # number of generator steps per discriminator step
    'random_seed': 42,
    'n_samples': 1000,  # number of synthetic rows to generate by default
    'desirable_g_loss': 1.0,  # higher threshold for generator loss
    'desirable_d_loss': 1.0   # higher threshold for discriminator loss
}
