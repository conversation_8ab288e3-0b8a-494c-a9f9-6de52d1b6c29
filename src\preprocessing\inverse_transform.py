import pandas as pd
import numpy as np
# inverse_transform.py

class InverseTransformer:
    def __init__(self, categorical_columns, numeric_columns, label_encoders, scalers):
        self.categorical_columns = categorical_columns
        self.numeric_columns = numeric_columns
        self.label_encoders = label_encoders
        self.scalers = scalers

    def inverse_transform(self, data, columns):
        df = pd.DataFrame(data, columns=columns)
        for col in self.categorical_columns:
            le = self.label_encoders[col]
            df[col] = np.round(df[col]).astype(int)
            df[col] = le.inverse_transform(df[col].clip(0, len(le.classes_)-1))
        for col in self.numeric_columns:
            scaler = self.scalers[col]
            df[col] = scaler.inverse_transform(df[[col]])
        return df
