# dry_run.py
import argparse
import sys
import os
sys.path.append(os.path.dirname(__file__))
from src.orchestrators.train_orchestrator import TrainOrchestrator
from src.orchestrators.generate_orchestrator import GenerateOrchestrator

def main():
    parser = argparse.ArgumentParser(description='Synthetic Data Generator Dry Run')
    parser.add_argument('mode', choices=['train', 'generate'], help='Mode to run: train or generate')
    parser.add_argument('--csv', type=str, help='Path to input CSV (optional, uses example if not provided)')
    parser.add_argument('--n_samples', type=int, default=1000, help='Number of synthetic samples to generate (for generate mode)')
    parser.add_argument('--output', type=str, default=None, help='Output CSV path for generated data (for generate mode)')
    parser.add_argument('--frontend', action='store_true', help='Use frontend input dataset')
    args = parser.parse_args()

    if args.mode == 'train':
        if hasattr(args, 'frontend') and args.frontend:
            # Placeholder: use frontend input dataset (to be implemented)
            csv_path = os.path.join('data', 'input', 'frontend_input.csv')
            print('[DryRun] Using frontend input dataset:', csv_path)
        elif args.csv:
            csv_path = args.csv
            print('[DryRun] Using CSV from argument:', csv_path)
        else:
            csv_path = None  # Use imported data (default in DataPreprocessor)
            print('[DryRun] Using imported data (default)')
        orchestrator = TrainOrchestrator(csv_path=csv_path)
        orchestrator.run()
    elif args.mode == 'generate':
        orchestrator = GenerateOrchestrator(csv_path=args.csv, n_samples=args.n_samples, output=args.output)
        orchestrator.run()

if __name__ == '__main__':
    main()
