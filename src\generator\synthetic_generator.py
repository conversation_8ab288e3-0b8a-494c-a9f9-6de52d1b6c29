import torch
import pandas as pd
import numpy as np
from src.model import Generator
from src.postprocessing import Denormalizer, InverseTransformer

class SyntheticGenerator:
    def __init__(self, preprocessor, settings):
        self.preprocessor = preprocessor
        self.settings = settings
        self.device = torch.device('cuda' if settings['cuda'] and torch.cuda.is_available() else 'cpu')
        import os
        local_results_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'local_results')
        self.generator = Generator(
            numeric_dim=len(preprocessor.numeric_columns),
            categorical_dims=[len(preprocessor.transformer.label_encoders[col].classes_) for col in preprocessor.categorical_columns],
            embedding_dim=settings['embedding_dim'],
            layers=settings['generator_layers'],
            dropout=settings.get('dropout', 0.0)
        ).to(self.device)
        self.generator.load_state_dict(torch.load(os.path.join(local_results_dir, 'generator_best.pth'), map_location=self.device))
        self.generator.eval()
        # Setup postprocessing
        self.denormalizer = Denormalizer(
            preprocessor.numeric_columns,
            preprocessor.transformer.scalers,
            getattr(preprocessor, 'numeric_decimals', None)
        )
        self.inverse_transformer = InverseTransformer(
            preprocessor.categorical_columns,
            preprocessor.transformer.label_encoders
        )


    def generate(self, n_samples):
        # Generate synthetic data using new model interface
        z_numeric = torch.randn(n_samples, len(self.preprocessor.numeric_columns), device=self.device)
        z_categorical = torch.stack([
            torch.randint(0, num_categories, (n_samples,), device=self.device)
            for num_categories in [len(self.preprocessor.transformer.label_encoders[col].classes_) for col in self.preprocessor.categorical_columns]
        ], dim=1)
        with torch.no_grad():
            synthetic = self.generator(z_numeric, z_categorical).cpu().numpy()
        # Split output
        num_numeric = len(self.preprocessor.numeric_columns)
        num_categorical = len(self.preprocessor.categorical_columns)
        synthetic_numeric = synthetic[:, :num_numeric]
        synthetic_categorical = synthetic[:, num_numeric:num_numeric+num_categorical].astype(int)
        # Diversity diagnostics
        print('[SyntheticGenerator] Synthetic numeric mean:', np.mean(synthetic_numeric, axis=0))
        print('[SyntheticGenerator] Synthetic numeric std:', np.std(synthetic_numeric, axis=0))
        print('[SyntheticGenerator] Synthetic categorical unique counts:', [np.unique(synthetic_categorical[:, i]).size for i in range(synthetic_categorical.shape[1])])
        print('[SyntheticGenerator] Synthetic categorical sample values:', [np.unique(synthetic_categorical[:, i])[:10] for i in range(synthetic_categorical.shape[1])])
        df = pd.DataFrame(
            np.concatenate([synthetic_numeric, synthetic_categorical], axis=1),
            columns=list(self.preprocessor.numeric_columns) + list(self.preprocessor.categorical_columns)
        )
        # Inverse transform categoricals to human-readable
        df = self.inverse_transformer.inverse_transform(df)
        # Denormalize numerics
        df = self.denormalizer.denormalize(df)
        # Reorder columns to match original
        df = df[self.preprocessor.original_columns]
        return df
