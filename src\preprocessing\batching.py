import pandas as pd
import numpy as np
# batching.py

class Batcher:
    def __init__(self, data, numeric_columns, categorical_columns, batch_size=128, random_seed=42):
        self.data = data
        self.numeric_columns = numeric_columns
        self.categorical_columns = categorical_columns
        self.batch_size = batch_size
        self.random_seed = random_seed

    def get_batches(self):
        data = self.data.copy()
        np.random.seed(self.random_seed)
        np.random.shuffle(data)
        for i in range(0, len(data), self.batch_size):
            batch = data[i:i+self.batch_size]
            batch_df = pd.DataFrame(batch, columns=self.numeric_columns + self.categorical_columns)
            # Only treat columns in self.categorical_columns as categorical (should be label encoded)
            categoricals = batch_df[self.categorical_columns].values.astype(np.int64)
            numerics = batch_df[self.numeric_columns].values.astype(np.float32)
            # Runtime check: ensure all categorical values are integers and within valid range
            for idx, col in enumerate(self.categorical_columns):
                min_val = categoricals[:, idx].min()
                max_val = categoricals[:, idx].max()
                # Removed batcher debug print
            yield numerics, categoricals
