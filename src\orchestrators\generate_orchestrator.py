
from settings import SETTINGS
from src.logger import Logger
from src.preprocessing import DataPreprocessor
from src.generator import SyntheticGenerator
import os
# generate_orchestrator.py
class GenerateOrchestrator:
    def __init__(self, csv_path=None, n_samples=1000, output=None):
        self.logger = Logger()
        self.preprocessor = DataPreprocessor(
            csv_path=csv_path,
            dataset_name=SETTINGS['dataset'],
            batch_size=SETTINGS['batch_size'],
            embedding_dim=SETTINGS['embedding_dim'],
            random_seed=SETTINGS['random_seed']
        )
        self.synth_gen = SyntheticGenerator(self.preprocessor, SETTINGS)
        self.n_samples = n_samples
        # Always use data/output as the default output directory
        self.output = output or os.path.join('data', 'output', 'synthetic.csv')

    def run(self):
        df = self.synth_gen.generate(self.n_samples)
        os.makedirs(os.path.dirname(self.output), exist_ok=True)
        df.to_csv(self.output, index=False)
        self.logger.info(f"Synthetic data saved to {self.output}")
