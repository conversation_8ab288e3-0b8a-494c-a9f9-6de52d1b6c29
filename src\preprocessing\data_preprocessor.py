
import warnings
warnings.filterwarnings('ignore')
import pandas as pd
import numpy as np
from sklearn.datasets import fetch_openml
from .batching import Batcher
from .transform import DataTransformer
from .inverse_transform import InverseTransformer

class DataPreprocessor:
    @property
    def original_columns(self):
        return list(self.data.columns)
    def __init__(self, csv_path=None, dataset_name=None, batch_size=128, embedding_dim=16, random_seed=42):
        # Default to adult_income.csv if no path provided
        if csv_path is None and dataset_name is None:
            csv_path = "data/input/adult_income.csv"
        self.csv_path = csv_path
        self.dataset_name = dataset_name
        self.batch_size = batch_size
        self.embedding_dim = embedding_dim
        self.random_seed = random_seed
        self.data = self._load_data()
        self.transformer = DataTransformer(embedding_dim)
        self.encoded_data = self.transformer.fit_transform(self.data)
        # Record decimal counts for numeric columns
        self.numeric_decimals = {}
        for col in self.transformer.numeric_columns:
            # Count max decimals in original column
            decimals = self.data[col].apply(lambda x: len(str(x).split('.')[-1]) if '.' in str(x) else 0)
            self.numeric_decimals[col] = decimals.max()
        # Check for NaN/Inf in encoded_data
        nan_mask = self.encoded_data.isna().any(axis=1) | self.encoded_data.applymap(np.isinf).any(axis=1)
        num_bad = nan_mask.sum()
        # Only print preprocessing logs if not in generation mode
        import inspect
        stack = inspect.stack()
        if not any('synthetic_generator' in frame.filename for frame in stack):
            if num_bad > 0:
                print(f"[DataPreprocessor] {num_bad} rows contain NaN or Inf after preprocessing.")
            else:
                print("[DataPreprocessor] No NaN or Inf values found after preprocessing.")
        # Ensure encoded_data columns are ordered as numeric_columns + categorical_columns
        ordered_columns = self.numeric_columns + self.categorical_columns
        self.encoded_data = self.encoded_data[ordered_columns]
        self.batcher = Batcher(self.encoded_data.values, self.numeric_columns, self.categorical_columns, batch_size, random_seed)
        self.inverse_transformer = InverseTransformer(
            self.transformer.categorical_columns,
            self.transformer.numeric_columns,
            self.transformer.label_encoders,
            self.transformer.scalers
        )

    def _load_data(self):
        if self.csv_path:
            return pd.read_csv(self.csv_path)
        elif self.dataset_name:
            return fetch_openml(self.dataset_name, as_frame=True).frame.dropna()
        else:
            raise ValueError('No valid data source provided.')

    def get_batches(self):
        return self.batcher.get_batches()

    @property
    def numeric_columns(self):
        return self.transformer.numeric_columns

    @property
    def categorical_columns(self):
        return self.transformer.categorical_columns

    def inverse_transform(self, data):
        return self.inverse_transformer.inverse_transform(data, self.encoded_data.columns)
