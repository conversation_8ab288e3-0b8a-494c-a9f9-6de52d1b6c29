
import pandas as pd
import numpy as np
# denormalization.py
class Denormalizer:
    def __init__(self, numeric_columns, scalers, numeric_decimals=None):
        self.numeric_columns = numeric_columns
        self.scalers = scalers
        self.numeric_decimals = numeric_decimals or {}

    def denormalize(self, df):
        for col in self.numeric_columns:
            scaler = self.scalers[col]
            df[col] = scaler.inverse_transform(df[[col]])
            # Only apply rounding to float columns
            if pd.api.types.is_float_dtype(df[col]):
                decimals = self.numeric_decimals.get(col, None)
                if decimals is not None and decimals > 0:
                    df[col] = df[col].round(decimals)
                elif decimals == 0:
                    df[col] = df[col].round(0).astype(int)
            elif pd.api.types.is_integer_dtype(df[col]):
                df[col] = df[col].astype(int)
        return df
