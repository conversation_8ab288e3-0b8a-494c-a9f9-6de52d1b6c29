import torch
import torch.nn as nn
import torch.nn.functional as F

class Generator(nn.Module):
    def __init__(self, numeric_dim, categorical_dims, embedding_dim=16, layers=None, dropout=0.0, feature_noise_std=0.05):
        super().__init__()
        self.embeddings = nn.ModuleList([
            nn.Embedding(num_categories, embedding_dim)
            for num_categories in categorical_dims
        ])
        self.numeric_dim = numeric_dim
        self.embedding_dim = embedding_dim
        input_dim = numeric_dim + embedding_dim * len(categorical_dims)
        print(f"[Generator] numeric_dim={numeric_dim}, embedding_dim={embedding_dim}, num_categoricals={len(categorical_dims)}, input_dim={input_dim}, total_categorical_dims={sum(categorical_dims)}")
        if layers is None:
            layers = [128, 128]
        net_layers = []
        prev_dim = input_dim
        for h in layers:
            linear = nn.Linear(prev_dim, h)
            # Xavier initialization for better gradient flow
            nn.init.xavier_uniform_(linear.weight)
            nn.init.zeros_(linear.bias)
            net_layers.append(linear)
            net_layers.append(nn.ReLU())
            if dropout > 0:
                net_layers.append(nn.Dropout(dropout))
            prev_dim = h

        # Calculate total output size: numeric_dim + sum of each categorical's possible values
        self.total_categorical_dims = sum(categorical_dims)
        self.numeric_dim = numeric_dim
        self.categorical_dims = categorical_dims

        # Final layer outputs logits for each category for each categorical variable
        final_layer = nn.Linear(prev_dim, numeric_dim + self.total_categorical_dims)
        # Smaller initialization for final layer to prevent saturation
        nn.init.xavier_uniform_(final_layer.weight, gain=0.5)
        nn.init.zeros_(final_layer.bias)
        net_layers.append(final_layer)
        self.net = nn.Sequential(*net_layers)
        self.feature_noise_std = feature_noise_std

        # Improved numeric output layer with better initialization
        self.numeric_out_layer = nn.Linear(numeric_dim, numeric_dim)
        nn.init.xavier_uniform_(self.numeric_out_layer.weight, gain=0.1)
        nn.init.zeros_(self.numeric_out_layer.bias)

        # Initialize embeddings properly
        for emb in self.embeddings:
            nn.init.xavier_uniform_(emb.weight)

    def forward(self, z_numeric, z_categorical):
        # Runtime check for embedding indices
        for i, emb in enumerate(self.embeddings):
            max_idx = emb.num_embeddings
            if (z_categorical[:, i] >= max_idx).any() or (z_categorical[:, i] < 0).any():
                bad_indices = (z_categorical[:, i] >= max_idx) | (z_categorical[:, i] < 0)
                print(f"[Generator] Invalid embedding indices in column {i}: {z_categorical[:, i][bad_indices].cpu().numpy()}")
                raise ValueError(f"Embedding index out of bounds for categorical column {i}")

        # Process input embeddings
        embedded = [emb(z_categorical[:, i]) for i, emb in enumerate(self.embeddings)]
        embedded = torch.cat(embedded, dim=1) if embedded else torch.empty((z_numeric.size(0), 0), device=z_numeric.device)
        x = torch.cat([z_numeric, embedded], dim=1)

        # Add feature noise for diversity (reduced for stability)
        if self.training and self.feature_noise_std > 0:
            noise = torch.randn_like(x) * self.feature_noise_std
            x = x + noise

        # Get network output
        output = self.net(x)

        # Split output into numeric and logits for each categorical variable
        numeric_raw = output[:, :self.numeric_dim]
        # Apply tanh activation and scale appropriately
        numeric_output = torch.tanh(numeric_raw) * 2.0  # Scale to [-2, 2] range
        # Apply additional scaling layer with clamping for stability
        numeric_output = self.numeric_out_layer(numeric_output)
        numeric_output = torch.clamp(numeric_output, -5.0, 5.0)  # Prevent extreme values

        start_idx = self.numeric_dim
        categorical_outputs = []
        # Get min/max for each categorical column from preprocessor
        if hasattr(self, 'categorical_minmax'):
            minmax = self.categorical_minmax
        else:
            minmax = [(0, num_cat-1) for num_cat in self.categorical_dims]

        for i, dim in enumerate(self.categorical_dims):
            logits = output[:, start_idx:start_idx + dim]
            # Apply temperature scaling for better categorical distribution
            temperature = 1.0
            logits = logits / temperature
            # Use Gumbel-Softmax for better gradient flow during training
            if self.training:
                # Gumbel noise for exploration
                gumbel_noise = -torch.log(-torch.log(torch.rand_like(logits) + 1e-8) + 1e-8)
                logits = logits + gumbel_noise * 0.1

            probs = F.softmax(logits, dim=1)
            indices = torch.argmax(probs, dim=1)
            min_val, max_val = minmax[i]
            indices = torch.clamp(indices, min_val, max_val)
            categorical_outputs.append(indices)
            start_idx += dim

        categorical_output = torch.stack(categorical_outputs, dim=1)
        # Return numeric and categorical outputs separately
        return numeric_output, categorical_output.long()

class Discriminator(nn.Module):
    def __init__(self, numeric_dim, categorical_dims, embedding_dim=16, layers=None, dropout=0.0, mbd_out_features=8, mbd_kernel_dim=4, label_smoothing=0.05):
        super().__init__()
        self.embeddings = nn.ModuleList([
            nn.Embedding(num_categories, embedding_dim)
            for num_categories in categorical_dims
        ])
        self.numeric_dim = numeric_dim
        self.embedding_dim = embedding_dim
        input_dim = numeric_dim + embedding_dim * len(categorical_dims)
        if layers is None:
            layers = [128, 128]

        # Reduced mini-batch discrimination for stability
        self.mbd_out_features = mbd_out_features
        self.mbd_kernel_dim = mbd_kernel_dim
        self.mbd = nn.Linear(input_dim, mbd_out_features * mbd_kernel_dim)
        # Initialize MBD layer properly
        nn.init.xavier_uniform_(self.mbd.weight, gain=0.1)
        nn.init.zeros_(self.mbd.bias)

        # Main network takes input_dim + mbd_out_features as input
        net_layers = []
        prev_dim = input_dim + mbd_out_features
        for h in layers:
            linear = nn.Linear(prev_dim, h)
            # He initialization for LeakyReLU
            nn.init.kaiming_uniform_(linear.weight, a=0.2, nonlinearity='leaky_relu')
            nn.init.zeros_(linear.bias)
            net_layers.append(linear)
            net_layers.append(nn.LeakyReLU(0.2))
            if dropout > 0:
                net_layers.append(nn.Dropout(dropout))
            prev_dim = h

        # Final layer with careful initialization
        final_layer = nn.Linear(prev_dim, 1)
        nn.init.xavier_uniform_(final_layer.weight, gain=0.1)
        nn.init.zeros_(final_layer.bias)
        net_layers.append(final_layer)

        self.net = nn.Sequential(*net_layers)
        self.label_smoothing = label_smoothing

        # Initialize embeddings properly
        for emb in self.embeddings:
            nn.init.xavier_uniform_(emb.weight)

    def forward(self, x_numeric, x_categorical):
        # Ensure categorical input is long type
        x_categorical = x_categorical.long()
        # Runtime check for embedding indices
        for i, emb in enumerate(self.embeddings):
            max_idx = emb.num_embeddings
            if (x_categorical[:, i] >= max_idx).any() or (x_categorical[:, i] < 0).any():
                bad_indices = (x_categorical[:, i] >= max_idx) | (x_categorical[:, i] < 0)
                print(f"[Discriminator] Invalid embedding indices in column {i}: {x_categorical[:, i][bad_indices].cpu().numpy()}")
                raise ValueError(f"Embedding index out of bounds for categorical column {i}")

        embedded = [emb(x_categorical[:, i]) for i, emb in enumerate(self.embeddings)]
        embedded = torch.cat(embedded, dim=1) if embedded else torch.empty((x_numeric.size(0), 0), device=x_numeric.device)
        x = torch.cat([x_numeric, embedded], dim=1)

        # Improved mini-batch discrimination with numerical stability
        mbd_features = self.mbd(x)
        mbd_features = mbd_features.view(-1, self.mbd_out_features, self.mbd_kernel_dim)

        # Compute L1 distance between samples in batch with stability improvements
        batch_size = mbd_features.size(0)
        if batch_size > 1:
            # Use more stable distance computation
            diffs = torch.abs(mbd_features.unsqueeze(0) - mbd_features.unsqueeze(1)).sum(3)
            # Clamp differences to prevent extreme values
            diffs = torch.clamp(diffs, 0, 10.0)
            # Use more stable activation instead of exp
            mbd_out = torch.sigmoid(-diffs * 0.1).sum(1)  # Scale down and use sigmoid
            # Normalize by batch size for consistency
            mbd_out = mbd_out / (batch_size - 1)
        else:
            # Handle single sample case
            mbd_out = torch.zeros(batch_size, self.mbd_out_features, device=x.device)

        x = torch.cat([x, mbd_out], dim=1)
        out = self.net(x)

        # Clamp output to prevent extreme values
        out = torch.clamp(out, -10.0, 10.0)

        # Reduced label smoothing for stability
        if self.label_smoothing > 0:
            out = out * (1 - self.label_smoothing) + self.label_smoothing * 0.5
        return out
