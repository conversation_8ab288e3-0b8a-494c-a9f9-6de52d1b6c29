import torch
import torch.nn as nn
import torch.nn.functional as F

class Generator(nn.Module):
    def __init__(self, numeric_dim, categorical_dims, embedding_dim=16, layers=None, dropout=0.0, feature_noise_std=0.1):
        super().__init__()
        self.embeddings = nn.ModuleList([
            nn.Embedding(num_categories, embedding_dim)
            for num_categories in categorical_dims
        ])
        self.numeric_dim = numeric_dim
        self.embedding_dim = embedding_dim
        input_dim = numeric_dim + embedding_dim * len(categorical_dims)
        print(f"[Generator] numeric_dim={numeric_dim}, embedding_dim={embedding_dim}, num_categoricals={len(categorical_dims)}, input_dim={input_dim}, total_categorical_dims={sum(categorical_dims)}")
        if layers is None:
            layers = [128, 128]
        net_layers = []
        prev_dim = input_dim
        for h in layers:
            net_layers.append(nn.Linear(prev_dim, h))
            net_layers.append(nn.ReLU())
            if dropout > 0:
                net_layers.append(nn.Dropout(dropout))
            prev_dim = h
        
        # Calculate total output size: numeric_dim + sum of each categorical's possible values
        self.total_categorical_dims = sum(categorical_dims)
        self.numeric_dim = numeric_dim
        self.categorical_dims = categorical_dims
        
        # Final layer outputs logits for each category for each categorical variable
        net_layers.append(nn.Linear(prev_dim, numeric_dim + self.total_categorical_dims))
        self.net = nn.Sequential(*net_layers)
        self.feature_noise_std = feature_noise_std
        self.numeric_out_layer = nn.Linear(numeric_dim, numeric_dim)
        nn.init.constant_(self.numeric_out_layer.weight, 1.0)
        nn.init.constant_(self.numeric_out_layer.bias, 0.0)

    def forward(self, z_numeric, z_categorical):
        # Runtime check for embedding indices
        for i, emb in enumerate(self.embeddings):
            max_idx = emb.num_embeddings
            if (z_categorical[:, i] >= max_idx).any() or (z_categorical[:, i] < 0).any():
                bad_indices = (z_categorical[:, i] >= max_idx) | (z_categorical[:, i] < 0)
                print(f"[Generator] Invalid embedding indices in column {i}: {z_categorical[:, i][bad_indices].cpu().numpy()}")
                raise ValueError(f"Embedding index out of bounds for categorical column {i}")
        # Process input embeddings
        embedded = [emb(z_categorical[:, i]) for i, emb in enumerate(self.embeddings)]
        embedded = torch.cat(embedded, dim=1) if embedded else torch.empty((z_numeric.size(0), 0), device=z_numeric.device)
        x = torch.cat([z_numeric, embedded], dim=1)
        
        # Add feature noise for diversity
        if self.feature_noise_std > 0:
            noise = torch.randn_like(x) * self.feature_noise_std
            x = x + noise
        
        # Get network output
        output = self.net(x)
        
        # Split output into numeric and logits for each categorical variable
        numeric_output = torch.tanh(output[:, :self.numeric_dim])
        numeric_output = self.numeric_out_layer(numeric_output)
        start_idx = self.numeric_dim
        categorical_outputs = []
        # Get min/max for each categorical column from preprocessor
        # This requires passing min/max as an argument or storing in the generator
        if hasattr(self, 'categorical_minmax'):
            minmax = self.categorical_minmax
        else:
            minmax = [(0, num_cat-1) for num_cat in self.categorical_dims]
        for i, dim in enumerate(self.categorical_dims):
            logits = output[:, start_idx:start_idx + dim]
            probs = F.softmax(logits, dim=1)
            indices = torch.argmax(probs, dim=1)
            min_val, max_val = minmax[i]
            indices = torch.clamp(indices, min_val, max_val)
            categorical_outputs.append(indices)
            start_idx += dim
        categorical_output = torch.stack(categorical_outputs, dim=1)
        # Return numeric and categorical outputs separately
        return numeric_output, categorical_output.long()

class Discriminator(nn.Module):
    def __init__(self, numeric_dim, categorical_dims, embedding_dim=16, layers=None, dropout=0.0, mbd_out_features=16, mbd_kernel_dim=8, label_smoothing=0.1):
        super().__init__()
        self.embeddings = nn.ModuleList([
            nn.Embedding(num_categories, embedding_dim)
            for num_categories in categorical_dims
        ])
        self.numeric_dim = numeric_dim
        self.embedding_dim = embedding_dim
        input_dim = numeric_dim + embedding_dim * len(categorical_dims)
        if layers is None:
            layers = [128, 128]
        # Mini-batch discrimination (define before main network)
        self.mbd_out_features = mbd_out_features
        self.mbd_kernel_dim = mbd_kernel_dim
        self.mbd = nn.Linear(input_dim, mbd_out_features * mbd_kernel_dim)
        
        # Main network takes input_dim + mbd_out_features as input
        net_layers = []
        prev_dim = input_dim + mbd_out_features
        for h in layers:
            net_layers.append(nn.Linear(prev_dim, h))
            net_layers.append(nn.LeakyReLU(0.2))
            if dropout > 0:
                net_layers.append(nn.Dropout(dropout))
            prev_dim = h
        net_layers.append(nn.Linear(prev_dim, 1))
        self.net = nn.Sequential(*net_layers)
        self.label_smoothing = label_smoothing

    def forward(self, x_numeric, x_categorical):
        # Ensure categorical input is long type
        x_categorical = x_categorical.long()
        # Runtime check for embedding indices
        for i, emb in enumerate(self.embeddings):
            max_idx = emb.num_embeddings
            if (x_categorical[:, i] >= max_idx).any() or (x_categorical[:, i] < 0).any():
                bad_indices = (x_categorical[:, i] >= max_idx) | (x_categorical[:, i] < 0)
                print(f"[Discriminator] Invalid embedding indices in column {i}: {x_categorical[:, i][bad_indices].cpu().numpy()}")
                raise ValueError(f"Embedding index out of bounds for categorical column {i}")
        embedded = [emb(x_categorical[:, i]) for i, emb in enumerate(self.embeddings)]
        embedded = torch.cat(embedded, dim=1) if embedded else torch.empty((x_numeric.size(0), 0), device=x_numeric.device)
        x = torch.cat([x_numeric, embedded], dim=1)
        # Mini-batch discrimination
        mbd_features = self.mbd(x)
        mbd_features = mbd_features.view(-1, self.mbd_out_features, self.mbd_kernel_dim)
        # Compute L1 distance between samples in batch
        diffs = torch.abs(mbd_features.unsqueeze(0) - mbd_features.unsqueeze(1)).sum(3)
        mbd_out = torch.exp(-diffs).sum(1)
        x = torch.cat([x, mbd_out], dim=1)
        out = self.net(x)
        # Label smoothing (for real/fake labels)
        if self.label_smoothing > 0:
            out = out * (1 - self.label_smoothing) + self.label_smoothing * 0.5
        return out
