# Synthetic Data Generator (GAN-based)

A modular, extensible codebase for generating synthetic, anonymized tabular data using GANs. Handles both numeric and categorical columns, with dynamic batching, normalization, embedding, and postprocessing. CUDA support and clear logging included.

## Features
- Train on any CSV or example dataset (numeric/categorical)
- Modular classes for data loading, preprocessing, GAN, training, and generation
- Embedding for categorical columns
- Normalization and inverse transforms
- Logging and progress bars
- Early stopping (patience)
- CUDA support
- Settings in `src/settings.py`
- Frontend-ready structure

## Usage
1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
2. Train:
   ```
   python -m src.app --mode train [--csv path/to/your.csv]
   ```
3. Generate synthetic data:
   ```
   python -m src.app --mode generate --n_samples 1000 --output synthetic.csv [--csv path/to/your.csv]
   ```
4. Dry run (inspect data):
   ```
   python -m src.app --mode dryrun [--csv path/to/your.csv]
   ```

## Frontend
See `src/frontend/` for future UI integration.
