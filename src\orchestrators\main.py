
import argparse
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from src.orchestrators.train_orchestrator import TrainOrchestrator
from src.orchestrators.generate_orchestrator import GenerateOrchestrator
# main.py
def main():
    parser = argparse.ArgumentParser(description='Synthetic Data Generator Main Orchestrator')
    parser.add_argument('mode', choices=['train', 'generate', 'preview'], help='Mode to run: train, generate, or preview')
    parser.add_argument('--csv', type=str, default=None, help='Path to input CSV (default: data/input/input.csv)')
    parser.add_argument('--n_samples', type=int, default=1000, help='Number of synthetic samples to generate (for generate mode)')
    parser.add_argument('--output', type=str, default=None, help='Output CSV path for generated data (for generate mode)')
    args = parser.parse_args()

    csv_path = args.csv or os.path.join('data', 'input', 'adult_income.csv')

    if args.mode == 'train':
        orchestrator = TrainOrchestrator(csv_path=csv_path)
        orchestrator.run()
    elif args.mode == 'generate':
        orchestrator = GenerateOrchestrator(csv_path=csv_path, n_samples=args.n_samples, output=args.output)
        orchestrator.run()
    elif args.mode == 'preview':
        from src.orchestrators.preview_orchestrator import run_preview
        run_preview()

if __name__ == '__main__':
    main()
