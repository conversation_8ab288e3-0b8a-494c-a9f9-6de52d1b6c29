import torch
import torch.optim as optim
from src.logger import Logger
from src.model import Generator, Discriminator
from src.preprocessing import DataPreprocessor
import pickle
import os
import pandas as pd
import time
from tqdm import tqdm
import numpy as np
import torch.nn.functional as F

class Trainer:
    def __init__(self, preprocessor, settings):
        self.logger = Logger()
        self.device = torch.device('cuda' if settings['cuda'] and torch.cuda.is_available() else 'cpu')
        self.preprocessor = preprocessor
        self.settings = settings
        # --- Validation checks for settings ---
        required_keys = [
            'latent_dim', 'embedding_dim', 'generator_layers', 'discriminator_layers',
            'dropout', 'gradient_clip', 'd_steps', 'g_steps', 'optimizer_g', 'optimizer_d',
            'learning_rate_g', 'learning_rate_d', 'weight_decay', 'epochs', 'batch_size', 'patience',
            'desirable_g_loss', 'desirable_d_loss'
        ]
        for k in required_keys:
            if k not in settings:
                raise ValueError(f"Missing required setting: {k}")
        if not isinstance(settings['generator_layers'], list) or not settings['generator_layers']:
            raise ValueError("generator_layers must be a non-empty list of integers")
        if not isinstance(settings['discriminator_layers'], list) or not settings['discriminator_layers']:
            raise ValueError("discriminator_layers must be a non-empty list of integers")
        if settings['dropout'] < 0 or settings['dropout'] > 1:
            raise ValueError("dropout must be between 0 and 1")
        if settings['gradient_clip'] is not None and settings['gradient_clip'] <= 0:
            raise ValueError("gradient_clip must be positive")
        if settings['d_steps'] < 1 or settings['g_steps'] < 1:
            raise ValueError("d_steps and g_steps must be >= 1")
        # --- End validation checks ---
        self.latent_dim = settings['latent_dim']
        self.embedding_dim = settings['embedding_dim']
        self.generator_layers = settings['generator_layers']
        self.discriminator_layers = settings['discriminator_layers']
        self.dropout = settings['dropout']
        self.gradient_clip = settings['gradient_clip']
        self.d_steps = settings['d_steps']
        self.g_steps = settings['g_steps']
        # Model initialization (once)
        self.generator = Generator(
            numeric_dim=len(preprocessor.numeric_columns),
            categorical_dims=[len(preprocessor.transformer.label_encoders[col].classes_) for col in preprocessor.categorical_columns],
            embedding_dim=self.embedding_dim,
            layers=self.generator_layers,
            dropout=self.dropout
        ).to(self.device)
        self.discriminator = Discriminator(
            numeric_dim=len(preprocessor.numeric_columns),
            categorical_dims=[len(preprocessor.transformer.label_encoders[col].classes_) for col in preprocessor.categorical_columns],
            embedding_dim=self.embedding_dim,
            layers=self.discriminator_layers,
            dropout=self.dropout
        ).to(self.device)
        # Preprocess all batches once and cache them
        from tqdm import tqdm
        raw_batches = list(preprocessor.get_batches())
        self.batches = []
        for batch_idx, (real_numeric, real_categorical) in enumerate(tqdm(raw_batches, desc="Preprocessing batches", unit="batch"), 1):
            real_numeric = torch.tensor(real_numeric, dtype=torch.float32, device=self.device)
            real_categorical = torch.tensor(real_categorical, dtype=torch.long, device=self.device)
            nan_mask = torch.isnan(real_numeric).any(dim=1) | torch.isinf(real_numeric).any(dim=1)
            if nan_mask.any():
                num_bad = nan_mask.sum().item()
                if num_bad < 5:
                    self.logger.info(f"Skipping {num_bad} rows with NaN/Inf in batch {batch_idx}")
                    real_numeric = real_numeric[~nan_mask]
                    real_categorical = real_categorical[~nan_mask]
                    if real_numeric.size(0) == 0:
                        continue
                else:
                    raise ValueError(f"Batch {batch_idx} contains {num_bad} rows with NaN/Inf values. Aborting.")
            self.batches.append((real_numeric, real_categorical))
        # One-time check: print mean and std of first real batch numerics
        if len(self.batches) > 0:
            real_numeric_sample = self.batches[0][0].cpu().numpy()
            print('[Trainer] First batch real numerics mean:', real_numeric_sample.mean(axis=0))
            print('[Trainer] First batch real numerics std:', real_numeric_sample.std(axis=0))
        # One-time check: print min, max, and unique values for real and fake categorical columns
        if len(self.batches) > 0:
            real_categorical_sample = self.batches[0][1].cpu().numpy()
            print('[Trainer] First batch real categoricals min:', real_categorical_sample.min(axis=0))
            print('[Trainer] First batch real categoricals max:', real_categorical_sample.max(axis=0))
            print('[Trainer] First batch real categoricals unique:', [np.unique(real_categorical_sample[:, i])[:10] for i in range(real_categorical_sample.shape[1])])
            # Generate a fake batch for comparison
            z_numeric = torch.randn(real_categorical_sample.shape[0], len(self.preprocessor.numeric_columns), device=self.device)
            z_categorical = torch.stack([
                torch.randint(0, num_categories, (real_categorical_sample.shape[0],), device=self.device)
                for num_categories in [len(self.preprocessor.transformer.label_encoders[col].classes_) for col in self.preprocessor.categorical_columns]
            ], dim=1)
            fake_numeric, fake_categorical = self.generator(z_numeric, z_categorical)
            fake_categorical_np = fake_categorical.cpu().numpy()
            print('[Trainer] First batch fake categoricals min:', fake_categorical_np.min(axis=0))
            print('[Trainer] First batch fake categoricals max:', fake_categorical_np.max(axis=0))
            print('[Trainer] First batch fake categoricals unique:', [np.unique(fake_categorical_np[:, i])[:10] for i in range(fake_categorical_np.shape[1])])
        # Check normalization and embedding for all batches
        all_numeric = []
        all_categorical = []
        for real_numeric, real_categorical in self.batches:
            all_numeric.append(real_numeric)
            all_categorical.append(real_categorical)
        all_numeric = torch.cat(all_numeric, dim=0).cpu().numpy()
        all_categorical = torch.cat(all_categorical, dim=0).cpu().numpy()
        print('[Trainer] All batches real numerics mean:', all_numeric.mean(axis=0))
        print('[Trainer] All batches real numerics std:', all_numeric.std(axis=0))
        print('[Trainer] All batches real numerics min:', all_numeric.min(axis=0))
        print('[Trainer] All batches real numerics max:', all_numeric.max(axis=0))
        print('[Trainer] All batches real categoricals min:', all_categorical.min(axis=0))
        print('[Trainer] All batches real categoricals max:', all_categorical.max(axis=0))
        # Optimizers with improved parameters
        opt_g = getattr(optim, settings.get('optimizer_g', 'Adam'))
        opt_d = getattr(optim, settings.get('optimizer_d', 'Adam'))

        # Use improved Adam parameters for stability
        adam_kwargs = {
            'lr': settings['learning_rate_g'],
            'weight_decay': settings.get('weight_decay', 0.0),
            'betas': (settings.get('beta1', 0.5), settings.get('beta2', 0.999)),
            'eps': settings.get('eps', 1e-8)
        }
        self.optim_g = opt_g(self.generator.parameters(), **adam_kwargs)

        adam_kwargs['lr'] = settings['learning_rate_d']
        self.optim_d = opt_d(self.discriminator.parameters(), **adam_kwargs)
        self.local_results_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'local_results')
        os.makedirs(self.local_results_dir, exist_ok=True)
        self.metrics = []
        self.desirable_g_loss = settings['desirable_g_loss']
        self.desirable_d_loss = settings['desirable_d_loss']
        # Set generator categorical min/max for realistic clamping
        if hasattr(self.generator, 'categorical_minmax'):
            pass
        else:
            cat_minmax = []
            for idx in range(len(self.preprocessor.categorical_columns)):
                min_val = int(self.batches[0][1][:, idx].min())
                max_val = int(self.batches[0][1][:, idx].max())
                cat_minmax.append((min_val, max_val))
            self.generator.categorical_minmax = cat_minmax

        # Compute real categorical distributions for regularization
        real_cat_counts = []
        for idx in range(len(self.preprocessor.categorical_columns)):
            vals, counts = np.unique(all_categorical[:, idx], return_counts=True)
            dist = np.zeros(int(all_categorical[:, idx].max())+1)
            dist[vals] = counts
            dist = dist / dist.sum()
            real_cat_counts.append(torch.tensor(dist, dtype=torch.float32, device=self.device))
        self.real_cat_dists = real_cat_counts

    def train(self):
        best_g_loss = float('inf')
        best_d_loss = float('inf')
        patience_counter = 0
        desirable_g_loss = self.desirable_g_loss
        desirable_d_loss = self.desirable_d_loss
        penalty_lambda = self.settings.get('penalty_lambda', 10.0)
        for epoch in range(self.settings['epochs']):
            g_loss_accum, d_loss_accum = 0, 0
            batches = self.batches
            num_batches = len(batches)
            progress = tqdm(batches, desc=f"Epoch {epoch+1}/{self.settings['epochs']}", unit="batch")
            epoch_start = time.time()
            for batch_idx, (real_numeric, real_categorical) in enumerate(progress, 1):
                elapsed = time.time() - epoch_start
                est_time_left = (elapsed / batch_idx) * (num_batches - batch_idx) if batch_idx > 0 else 0
                progress.set_postfix({
                    "Batch": f"{batch_idx}/{num_batches}",
                    "Elapsed": f"{elapsed:.1f}s",
                    "Est. left": f"{est_time_left/60:.1f} min"
                })
                real_numeric = torch.tensor(real_numeric, dtype=torch.float32, device=self.device)
                real_categorical = torch.tensor(real_categorical, dtype=torch.long, device=self.device)
                # Ensure data types are correct
                real_numeric = real_numeric.float()
                real_categorical = real_categorical.long()
                # Check for NaN/Inf in batch
                nan_mask = torch.isnan(real_numeric).any(dim=1) | torch.isinf(real_numeric).any(dim=1)
                if nan_mask.any():
                    num_bad = nan_mask.sum().item()
                    if num_bad < 5:
                        self.logger.info(f"Skipping {num_bad} rows with NaN/Inf in batch {batch_idx}")
                        real_numeric = real_numeric[~nan_mask]
                        real_categorical = real_categorical[~nan_mask]
                        if real_numeric.size(0) == 0:
                            continue
                    else:
                        raise ValueError(f"Batch {batch_idx} contains {num_bad} rows with NaN/Inf values. Aborting.")

                # WGAN-GP training with stability improvements
                batch_size = real_numeric.size(0)

                # Train discriminator
                for _ in range(self.d_steps):
                    z_numeric = torch.randn(batch_size, len(self.preprocessor.numeric_columns), device=self.device)
                    z_categorical = torch.stack([
                        torch.randint(0, num_categories, (batch_size,), device=self.device)
                        for num_categories in [len(self.preprocessor.transformer.label_encoders[col].classes_) for col in self.preprocessor.categorical_columns]
                    ], dim=1)

                    fake_numeric, fake_categorical = self.generator(z_numeric, z_categorical)
                    fake_numeric = fake_numeric.detach()
                    fake_categorical = fake_categorical.detach()

                    # Check for NaN/Inf in generated data
                    if torch.isnan(fake_numeric).any() or torch.isinf(fake_numeric).any():
                        print(f"[Trainer] Warning: NaN/Inf detected in generated numeric data at epoch {epoch+1}, batch {batch_idx}")
                        continue

                    d_real = self.discriminator(real_numeric, real_categorical)
                    d_fake = self.discriminator(fake_numeric, fake_categorical)

                    # Check for NaN/Inf in discriminator outputs
                    if torch.isnan(d_real).any() or torch.isinf(d_real).any() or torch.isnan(d_fake).any() or torch.isinf(d_fake).any():
                        print(f"[Trainer] Warning: NaN/Inf detected in discriminator outputs at epoch {epoch+1}, batch {batch_idx}")
                        continue

                    # Wasserstein loss with stability checks
                    d_loss = d_fake.mean() - d_real.mean()

                    # Gradient penalty with improved stability
                    if penalty_lambda > 0:
                        alpha = torch.rand(batch_size, 1, device=self.device)
                        interpolated_numeric = alpha * real_numeric + (1 - alpha) * fake_numeric

                        # Since categorical variables are discrete, only compute gradient penalty on numeric features
                        interpolated_numeric.requires_grad_(True)
                        d_interpolated = self.discriminator(interpolated_numeric, real_categorical)

                        # Compute gradients with respect to numeric features only
                        gradients = torch.autograd.grad(
                            outputs=d_interpolated,
                            inputs=[interpolated_numeric],
                            grad_outputs=torch.ones_like(d_interpolated),
                            create_graph=True,
                            retain_graph=True,
                            only_inputs=True
                        )[0]

                        # Compute gradient norm using only numeric gradients
                        grad_norm = gradients.view(batch_size, -1).norm(2, dim=1)
                        gradient_penalty = penalty_lambda * ((grad_norm - 1) ** 2).mean()

                        # Check for NaN/Inf in gradient penalty
                        if torch.isnan(gradient_penalty).any() or torch.isinf(gradient_penalty).any():
                            print(f"[Trainer] Warning: NaN/Inf detected in gradient penalty at epoch {epoch+1}, batch {batch_idx}")
                            gradient_penalty = torch.tensor(0.0, device=self.device)

                        d_loss = d_loss + gradient_penalty

                    # Check final discriminator loss
                    if torch.isnan(d_loss).any() or torch.isinf(d_loss).any():
                        print(f"[Trainer] Warning: NaN/Inf detected in discriminator loss at epoch {epoch+1}, batch {batch_idx}")
                        continue

                    self.optim_d.zero_grad()
                    d_loss.backward()

                    # Monitor gradient norms
                    d_grad_norm = torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), float('inf'))
                    if self.gradient_clip and d_grad_norm > self.gradient_clip:
                        torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), self.gradient_clip)

                    self.optim_d.step()

                # Train generator with stability improvements
                for _ in range(self.g_steps):
                    z_numeric = torch.randn(batch_size, len(self.preprocessor.numeric_columns), device=self.device)
                    z_categorical = torch.stack([
                        torch.randint(0, num_categories, (batch_size,), device=self.device)
                        for num_categories in [len(self.preprocessor.transformer.label_encoders[col].classes_) for col in self.preprocessor.categorical_columns]
                    ], dim=1)

                    fake_numeric, fake_categorical = self.generator(z_numeric, z_categorical)

                    # Check for NaN/Inf in generated data
                    if torch.isnan(fake_numeric).any() or torch.isinf(fake_numeric).any():
                        print(f"[Trainer] Warning: NaN/Inf detected in generator output at epoch {epoch+1}, batch {batch_idx}")
                        continue

                    d_fake = self.discriminator(fake_numeric, fake_categorical)

                    # Check for NaN/Inf in discriminator output
                    if torch.isnan(d_fake).any() or torch.isinf(d_fake).any():
                        print(f"[Trainer] Warning: NaN/Inf detected in discriminator output for generator training at epoch {epoch+1}, batch {batch_idx}")
                        continue

                    g_loss = -d_fake.mean()

                    # Categorical regularization: match real distribution (with stability)
                    cat_reg_loss = 0.0
                    if len(self.preprocessor.categorical_columns) > 0:
                        for idx in range(fake_categorical.shape[1]):
                            fake_dist = torch.bincount(fake_categorical[:, idx], minlength=self.real_cat_dists[idx].shape[0]).float()
                            fake_dist = fake_dist / (fake_dist.sum() + 1e-8)
                            # Add small epsilon to prevent log(0)
                            fake_dist = fake_dist + 1e-8
                            kl_loss = F.kl_div(fake_dist.log(), self.real_cat_dists[idx], reduction='batchmean')
                            # Check for NaN/Inf in KL divergence
                            if not (torch.isnan(kl_loss).any() or torch.isinf(kl_loss).any()):
                                cat_reg_loss += kl_loss

                    # Reduced regularization weight for stability
                    g_loss = g_loss + 0.05 * cat_reg_loss

                    # Check final generator loss
                    if torch.isnan(g_loss).any() or torch.isinf(g_loss).any():
                        print(f"[Trainer] Warning: NaN/Inf detected in generator loss at epoch {epoch+1}, batch {batch_idx}")
                        continue

                    self.optim_g.zero_grad()
                    g_loss.backward()

                    # Monitor gradient norms
                    g_grad_norm = torch.nn.utils.clip_grad_norm_(self.generator.parameters(), float('inf'))
                    if self.gradient_clip and g_grad_norm > self.gradient_clip:
                        torch.nn.utils.clip_grad_norm_(self.generator.parameters(), self.gradient_clip)

                    self.optim_g.step()

                g_loss_accum += g_loss.item()
                d_loss_accum += d_loss.item()
                # No per-batch logging; only update progress bar
                self.metrics.append({
                    'epoch': epoch+1,
                    'batch': batch_idx,
                    'g_loss': g_loss.item(),
                    'd_loss': d_loss.item(),
                    'patience': patience_counter
                })

            epoch_time = time.time() - epoch_start if 'epoch_start' in locals() else 0
            avg_g_loss = g_loss_accum / (batch_idx)
            avg_d_loss = d_loss_accum / (batch_idx)
            est_time_left = epoch_time * (self.settings['epochs'] - (epoch+1))
            log_msg = (
                f"Epoch {epoch+1}: "
                f"Avg G_loss={avg_g_loss:.4f}, "
                f"Avg D_loss={avg_d_loss:.4f}, "
                f"Accum G_loss={g_loss_accum:.4f}, "
                f"Accum D_loss={d_loss_accum:.4f}, "
                f"Patience={patience_counter}, "
                f"Time elapsed={epoch_time:.2f}s, "
                f"Est. time left={est_time_left/60:.1f} min"
            )
            # Patience logic: save if both losses improve or reach desirable thresholds
            improved = avg_g_loss < best_g_loss and avg_d_loss < best_d_loss
            reached_desirable = avg_g_loss <= desirable_g_loss and avg_d_loss <= desirable_d_loss
            if improved or reached_desirable:
                log_msg += " | Best model/metrics so far: saving generator weights and metrics."
                best_g_loss = avg_g_loss
                best_d_loss = avg_d_loss
                patience_counter = 0
                torch.save(self.generator.state_dict(), os.path.join(self.local_results_dir, 'generator_best.pth'))
                with open(os.path.join(self.local_results_dir, 'preprocessor.pkl'), 'wb') as f:
                    pickle.dump(self.preprocessor, f)
                pd.DataFrame(self.metrics).to_csv(os.path.join(self.local_results_dir, 'training_metrics.csv'), index=False)
            else:
                log_msg += " | Model not improved."
                patience_counter += 1
                self.logger.info(f"Patience increased to {patience_counter}.")
                if patience_counter >= self.settings['patience']:
                    self.logger.info('Early stopping triggered.')
                    break
            self.logger.info(log_msg)
