from settings import SETTINGS
from src.logger import Logger
from src.preprocessing import DataPreprocessor
from src.trainer import Trainer
# train_orchestrator.py

class TrainOrchestrator:
    def __init__(self, csv_path=None):
        self.logger = Logger()
        self.preprocessor = DataPreprocessor(
            csv_path=csv_path,
            batch_size=SETTINGS['batch_size'],
            embedding_dim=SETTINGS['embedding_dim'],
            random_seed=SETTINGS['random_seed']
        )
        self.trainer = Trainer(self.preprocessor, SETTINGS)

    def run(self):
        self.trainer.train()
